import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { KeycloakService } from '../../services/keycloak.service';

export interface ColumnDefinition {
  label: string;
  column: number;
}

export interface MultiColumnDropdownOption {
  [key: string]: any;
}

export interface MultiColumnApiResponse {
  data: MultiColumnDropdownOption[];
  columnDef?: { [key: string]: ColumnDefinition };
}

export interface ProcessedColumnData {
  columns: SortedColumn[];
  options: MultiColumnDropdownOption[];
  hasMultipleColumns: boolean;
}

export interface SortedColumn {
  fieldName: string;
  label: string;
  column: number;
}

@Injectable({
  providedIn: 'root'
})
export class MultiColumnDropdownService {
  
  // Cache for API responses and column definitions
  private dataCache: { [key: string]: MultiColumnDropdownOption[] } = {};
  private columnDefCache: { [key: string]: { [key: string]: ColumnDefinition } } = {};
  
  // Subject for real-time updates
  private cacheUpdated$ = new BehaviorSubject<string>('');

  constructor(
    private http: HttpClient,
    private keycloakService: KeycloakService
  ) {}

  /**
   * Load dropdown data with auto-detection of column structure
   */
  loadDropdownData(
    queryBuilderId: string, 
    searchTerm?: string, 
    limit: number = 20
  ): Observable<ProcessedColumnData> {
    
    const apiUrl = this.getApiUrl(queryBuilderId);
    const payload = this.buildApiPayload(queryBuilderId, searchTerm, limit);
    
    return this.http.post<MultiColumnApiResponse>(apiUrl, payload, this.getAuthHeaders())
      .pipe(
        map(response => this.processApiResponse(queryBuilderId, response))
      );
  }

  /**
   * Process API response and extract column definitions
   */
  private processApiResponse(
    queryBuilderId: string, 
    response: MultiColumnApiResponse
  ): ProcessedColumnData {
    
    let responseData: MultiColumnDropdownOption[] = [];
    let columnDef: { [key: string]: ColumnDefinition } = {};

    // Extract data array dynamically
    responseData = this.extractData(response);

    // Extract or auto-detect column definitions dynamically
    columnDef = this.extractColumnDef(response);
    if (!this.hasData(columnDef)) {
      // Auto-detect column definitions if not provided
      if (this.hasData(responseData)) {
        // Auto-detect from first data item
        columnDef = this.autoDetectColumns(responseData[0]);
      }
    }

    // Cache the results
    this.dataCache[queryBuilderId] = responseData;
    this.columnDefCache[queryBuilderId] = columnDef;

    // Process and sort columns
    const sortedColumns = this.sortColumnsByOrder(columnDef);
    
    return {
      columns: sortedColumns,
      options: responseData,
      hasMultipleColumns: sortedColumns.length > 1
    };
  }

  /**
   * Auto-detect column structure from data sample
   */
  private autoDetectColumns(sampleData: any): { [key: string]: ColumnDefinition } {
    const autoColumnDef: { [key: string]: ColumnDefinition } = {};
    let columnIndex = 1;

    Object.keys(sampleData).forEach(fieldName => {
      autoColumnDef[fieldName] = {
        label: fieldName, // Use field name as label
        column: columnIndex++
      };
    });

    return autoColumnDef;
  }

  /**
   * Sort columns by their column number
   */
  public sortColumnsByOrder(columnDef: { [key: string]: ColumnDefinition }, limitColumns: boolean = false): SortedColumn[] {
    const sortedColumns = Object.entries(columnDef)
      .sort(([,a], [,b]) => a.column - b.column)
      .map(([fieldName, def]) => ({
        fieldName,
        label: def.label || fieldName,
        column: def.column
      }));

    console.log('Sorted columns before limiting:', sortedColumns);
    console.log('Limit columns:', limitColumns);

    // Return only first 6 columns if limitColumns is true
    const result = limitColumns ? sortedColumns.slice(0, 6) : sortedColumns;
    console.log('Final columns returned:', result);

    return result;
  }

  public getAllColumns(columnDef: { [key: string]: ColumnDefinition }): SortedColumn[] {
    return this.sortColumnsByOrder(columnDef, false);
  }

  public getLimitedColumns(columnDef: { [key: string]: ColumnDefinition }): SortedColumn[] {
    return this.sortColumnsByOrder(columnDef, true);
  }

  /**
   * Build API payload without hardcoded field selection
   */
  private buildApiPayload(
    queryBuilderId: string, 
    searchTerm?: string, 
    limit: number = 20
  ): any {
    
    const payload: any = {
      _limit: limit
    };

    // Add dynamic search filters if we have cached column structure
    if (searchTerm && !this.isEmpty(searchTerm)) {
      const cachedColumnDef = this.columnDefCache[queryBuilderId];

      if (cachedColumnDef) {
        // Add search filters for all known fields
        Object.keys(cachedColumnDef).forEach(fieldName => {
          payload[fieldName] = {
            CT: searchTerm // Contains operator
          };
        });
      }
      // If no cached structure, let server handle the search or do client-side filtering
    }

    return payload;
  }

  /**
   * Get API URL for query builder
   */
  private getApiUrl(queryBuilderId: string): string {
    return `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
  }

  /**
   * Get authentication headers
   */
  private getAuthHeaders() {
    const token = this.keycloakService.getToken();
    return {
      headers: new HttpHeaders({
        Authorization: `Bearer ${token}`
      })
    };
  }

  /**
   * Filter options client-side (fallback for unknown structures)
   */
  filterOptionsClientSide(
    options: MultiColumnDropdownOption[],
    searchTerm: string
  ): MultiColumnDropdownOption[] {

    if (this.isEmpty(searchTerm)) {
      return options;
    }

    const lowerSearchTerm = searchTerm.toLowerCase();
    
    return options.filter(option => {
      return Object.values(option).some(value => 
        value && value.toString().toLowerCase().includes(lowerSearchTerm)
      );
    });
  }

  /**
   * Get cached data
   */
  getCachedData(queryBuilderId: string): MultiColumnDropdownOption[] | null {
    return this.dataCache[queryBuilderId] || null;
  }

  /**
   * Get cached column definitions
   */
  getCachedColumnDef(queryBuilderId: string): { [key: string]: ColumnDefinition } | null {
    return this.columnDefCache[queryBuilderId] || null;
  }

  /**
   * Clear cache for specific query builder
   */
  clearCache(queryBuilderId: string): void {
    delete this.dataCache[queryBuilderId];
    delete this.columnDefCache[queryBuilderId];
  }

  /**
   * Clear all cache
   */
  clearAllCache(): void {
    this.dataCache = {};
    this.columnDefCache = {};
  }

  /**
   * Get display text for an option (for input field display)
   */
  getOptionDisplayText(
    option: MultiColumnDropdownOption, 
    columns: SortedColumn[]
  ): string {
    
    if (!option || !columns || columns.length === 0) {
      return '';
    }

    // Use the first column (ordered by column definition) as primary display
    if (this.hasData(columns)) {
      const firstColumn = columns[0];
      if (option[firstColumn.fieldName]) {
        return option[firstColumn.fieldName].toString();
      }
    }

    // Fallback to first non-empty property
    const keys = Object.keys(option).filter(key =>
      option[key] && !this.isEmpty(option[key])
    );

    if (this.hasData(keys)) {
      return option[keys[0]].toString();
    }

    return '';
  }

  /**
   * Get option value for form control (completely dynamic)
   */
  getOptionValue(option: MultiColumnDropdownOption): any {
    if (!option) return '';

    // Completely dynamic: use the first available field from the actual data
    const keys = Object.keys(option).filter(key =>
      option[key] !== undefined &&
      option[key] !== null &&
      !this.isEmpty(option[key])
    );

    if (this.hasData(keys)) {
      return option[keys[0]];
    }

    return '';
  }

  // ===== UNIVERSAL DYNAMIC METHODS - NO HARDCODED ASSUMPTIONS =====

  // Universal data existence check
  private hasData(data: any): boolean {
    if (!data) return false;
    return Object.keys(data).length > 0;
  }

  // Universal empty value check
  private isEmpty(value: any): boolean {
    if (value == null) return true;
    const stringValue = value.toString ? value.toString() : '';
    return stringValue === '' || stringValue === 'null' || stringValue === 'undefined';
  }

  // Universal data extraction from any response structure
  private extractData(response: any): any[] {
    if (!response) return [];

    // Find ANY array property in response
    for (const key of Object.keys(response)) {
      const value = response[key];
      if (value && value.constructor === Array) {
        return value;
      }
    }

    // If response itself is array-like
    if (response.constructor === Array) {
      return response;
    }

    return [];
  }

  // Universal column definitions extraction
  private extractColumnDef(response: any): any {
    if (!response) return {};

    // Find ANY object property that could be column definitions
    for (const key of Object.keys(response)) {
      const value = response[key];
      if (value && typeof value === 'object' && value.constructor === Object) {
        // Check if it looks like column definitions
        const firstKey = Object.keys(value)[0];
        if (firstKey && value[firstKey] && value[firstKey].label !== undefined) {
          return value;
        }
      }
    }

    return {};
  }
}
