 import { Component, Input, Output, EventE<PERSON>ter, OnDestroy, OnInit, OnChanges, SimpleChanges, inject, ChangeDetectorRef } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { environment } from '../../../../environments/environment';
import { KeycloakService } from '../../../services/keycloak.service';

export interface DropdownOption {
  [key: string]: any;
}

export interface ColumnDefinition {
  label: string;
  column: number;
}

export interface DropdownConfig {
  type?: string; // Dynamic type - can be any string
  apiEndpoint?: string;
  queryBuilderId?: string;
  searchEnabled?: boolean;
  placeholder?: string;
  emptyMessage?: string;
  tooltip?: string;
  maxHeight?: string;
  limit?: number;
  options?: DropdownOption[]; // For preloaded options (instead of API calls)
}

export interface DropdownValueChangeEvent {
  fieldName: string;
  value: any;
  option: DropdownOption;
  displayText: string;
}

@Component({
  selector: 'app-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './dropdown.component.html',
  styleUrl: './dropdown.component.scss'
})
export class DropdownComponent implements OnInit, OnDestroy, OnChanges {
  // Core inputs
  @Input() fieldName!: string;
  @Input() formControl!: FormControl;
  @Input() config!: DropdownConfig;
  @Input() isDisabled: boolean = false;
  @Input() isReadonly: boolean = false;
  @Input() options: DropdownOption[] = [];
  @Input() selectedValue: any = '';
  @Input() cssClass: string = '';
  @Input() inputId?: string;

  // Advanced configuration
  @Input() preloadedData: { [key: string]: DropdownOption[] } = {};
  @Input() fields: any[] = []; // For extracting original field names
  @Input() searchDebounceTime: number = 300;
  @Input() showArrowButton: boolean = true;
  @Input() autoClose: boolean = true;

  // Outputs
  @Output() valueChange = new EventEmitter<DropdownValueChangeEvent>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() dropdownToggle = new EventEmitter<boolean>();
  @Output() optionSelect = new EventEmitter<DropdownOption>();

  // Internal state
  showDropdown: boolean = false;
  filteredOptions: DropdownOption[] = [];
  isLoading: boolean = false;
  searchTimeout: any;

  // Performance optimization: API response cache
  private apiCache: { [key: string]: DropdownOption[] } = {};

  // Track when we're setting dropdown values to prevent input conflicts
  private settingDropdownValue: boolean = false;

  // Unique identifier for this dropdown instance
  public uniqueId: string = '';

  // Column definitions from API response
  public columnDefinitions: { [key: string]: ColumnDefinition } = {};

  // Ordered display fields based on column definitions
  public orderedDisplayFields: string[] = [];

  private http = inject(HttpClient);
  private keycloakService = inject(KeycloakService);
  private cdr = inject(ChangeDetectorRef);

  private getAuthHeaders() {
    const token = this.keycloakService.getToken();
    return {
      headers: new HttpHeaders({
        Authorization: `Bearer ${token}`
      })
    };
  }

  ngOnInit() {
    // Generate unique identifier for this dropdown instance
    this.uniqueId = this.inputId || `${this.fieldName}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    
    // Initialize with preloaded data if available
    if (this.preloadedData && Object.keys(this.preloadedData).length > 0) {
      this.apiCache = { ...this.preloadedData };
    }

    // Preload dropdown data for performance
    this.preloadDropdownData();

    // Set initial filtered options if options are provided
    if (this.options && this.options.length > 0) {
      this.filteredOptions = [...this.options];
      
      // Update cache for preloaded options
      if (this.options && this.options.length > 0) {
        const cacheKey = this.getCacheKey();
        this.apiCache[cacheKey] = this.options;
      }
    }

    // Update disabled state
    this.updateFormControlDisabledState();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Update form control disabled state when inputs change
    if (changes['isDisabled'] || changes['isReadonly']) {
      this.updateFormControlDisabledState();
    }

    // Update filtered options when options input changes
    if (changes['options'] && this.options) {
      this.filteredOptions = [...this.options];
      
      // Update cache for preloaded options
      if (this.options && this.options.length > 0) {
        const cacheKey = this.getCacheKey();
        this.apiCache[cacheKey] = this.options;
      }
    }

    // Update cache when preloaded data changes
    if (changes['preloadedData'] && this.preloadedData) {
      this.apiCache = { ...this.preloadedData };
    }
  }

  ngOnDestroy() {
    // Clear search timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  }

  private updateFormControlDisabledState(): void {
    if (this.formControl) {
      if (this.isDisabled || this.isReadonly) {
        if (this.formControl.enabled) {
          this.formControl.disable();
        }
      } else {
        if (this.formControl.disabled) {
          this.formControl.enable();
        }
      }
    }
  }

  toggleDropdown(): void {
    // Prevent interaction when disabled/readonly
    if (this.isDisabled || this.isReadonly) {
      return;
    }

    if (!this.showDropdown) {
      const currentValue = this.formControl?.value || '';
      if (currentValue.trim() === '') {
        this.loadAllOptions();
      } else {
        this.searchOptions(currentValue);
      }
    } else {
      this.showDropdown = false;
    }

    this.dropdownToggle.emit(this.showDropdown);
  }

  onInputChange(event: Event): void {
    if (this.settingDropdownValue) {
      return; // Prevent conflicts when setting dropdown values
    }

    const target = event.target as HTMLInputElement;
    const searchTerm = target.value;

    // Clear existing timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Debounce search
    this.searchTimeout = setTimeout(() => {
      this.searchOptions(searchTerm);
      this.searchChange.emit(searchTerm);
    }, this.searchDebounceTime);
  }

  onInputFocus(): void {
    // Auto-open dropdown on focus if not disabled
    if (!this.isDisabled && !this.isReadonly && !this.showDropdown) {
      this.toggleDropdown();
    }
  }

  onInputBlur(): void {
    // Delay hiding dropdown to allow click on dropdown items
    if (this.autoClose) {
      setTimeout(() => {
        this.showDropdown = false;
        this.dropdownToggle.emit(false);
      }, 200);
    }
  }

  selectOption(option: DropdownOption): void {
    this.setDropdownValue(option);
    this.optionSelect.emit(option);
  }

  searchOptions(searchTerm: string): void {
    if (searchTerm.trim() === '') {
      this.loadAllOptions();
      return;
    }

    // If preloaded options are available, use client-side filtering
    if (this.options && this.options.length > 0) {
      this.loadAllAndFilter(searchTerm);
      return;
    }

    // Otherwise, use server-side filtering via API
    this.loadFromApi(searchTerm);
  }

  loadAllOptions(): void {
    const cacheKey = this.getCacheKey();

    // Handle preloaded options
    if (this.options && this.options.length > 0) {
      this.filteredOptions = [...this.options];
      this.showDropdown = true;
      return;
    }

    // Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      this.filteredOptions = this.apiCache[cacheKey];
      this.showDropdown = true;
      return;
    }

    // Fallback: Load if not preloaded
    this.loadFromApi();
  }

  private getCacheKey(): string {
    // For dynamic system, always use queryBuilderId if available
    if (this.config.queryBuilderId) {
      return this.config.queryBuilderId;
    }

    // Dynamic fallback: use field name or type as cache key
    return this.fieldName || this.config.type || 'default';
  }

  private loadFromApi(searchTerm?: string): void {
    // Handle preloaded options - use client-side filtering
    if (this.options && this.options.length > 0) {
      if (searchTerm && searchTerm.trim() !== '') {
        // Filter preloaded options based on search term
        const filtered = this.options.filter(option => {
          return this.matchesSearchTerm(option, searchTerm);
        });
        this.filteredOptions = filtered;
      } else {
        this.filteredOptions = [...this.options];
      }
      this.showDropdown = true;
      return;
    }

    const apiUrl = this.getApiUrl();
    const payload = this.getApiPayload(searchTerm);

    if (!apiUrl) {
      this.setEmptyDropdownState();
      return;
    }

    this.isLoading = true;

    this.http.post<any[]>(apiUrl, payload, this.getAuthHeaders()).subscribe({
      next: (response: any) => {
        this.isLoading = false;

        // Handle the API response structure with data property
        let responseData: any[] = [];
        if (response && response.data && Array.isArray(response.data)) {
          responseData = response.data;

          // Extract column definitions if available
          if (response.columnDef) {
            this.columnDefinitions = response.columnDef;
            this.orderedDisplayFields = this.getOrderedDisplayFields();
          }
        } else if (Array.isArray(response)) {
          responseData = response;
        }

        if (responseData.length > 0) {
          // Only cache if no search term (full data)
          if (!searchTerm || searchTerm.trim() === '') {
            const cacheKey = this.getCacheKey();
            this.apiCache[cacheKey] = responseData;
          }
          this.filteredOptions = responseData;
          this.showDropdown = true;
        } else {
          this.setEmptyDropdownState();
        }
      },
      error: () => {
        this.isLoading = false;
        this.setEmptyDropdownState();
      }
    });
  }

  private getApiUrl(): string {
    if (this.config.apiEndpoint) {
      return this.config.apiEndpoint;
    }

    // If preloaded options are available, no API endpoint needed
    if (this.options && this.options.length > 0) {
      return '';
    }

    const queryBuilderId = this.getCacheKey();
    return `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
  }

  private getApiPayload(searchTerm?: string): any {
    const basePayload: any = {};

    // For flexible dropdown, don't hardcode _select fields
    // Let the API return all available fields and use columnDef to determine display

    // Add server-side filtering if search term is provided
    if (searchTerm && searchTerm.trim() !== '') {
      // For flexible search, we'll search across all text fields
      // The API should handle this appropriately based on the data structure
      basePayload._search = searchTerm;
    }

    // Add _limit for performance
    if (this.config.limit) {
      basePayload._limit = this.config.limit;
    } else {
      basePayload._limit = 50; // Increased default for better UX
    }

    return basePayload;
  }

  private loadAllAndFilter(searchTerm: string): void {
    const cacheKey = this.getCacheKey();

    // Handle preloaded options - use client-side filtering
    if (this.options && this.options.length > 0) {
      const filtered = this.options.filter(option => {
        return this.matchesSearchTerm(option, searchTerm);
      });
      this.filteredOptions = filtered;
      this.showDropdown = true;
      return;
    }

    // Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      const filtered = this.apiCache[cacheKey].filter(option => {
        // Use the flexible search method for all dropdown types
        return this.matchesSearchTerm(option, searchTerm);
      });
      this.filteredOptions = filtered;
      this.showDropdown = true;
      return;
    }

    // Fallback: Load all data from API and then filter client-side
    const apiUrl = this.getApiUrl();
    const payload = this.getApiPayload(); // No search term for client-side filtering

    if (!apiUrl) {
      this.setEmptyDropdownState();
      return;
    }

    this.isLoading = true;
    this.http.post<any[]>(apiUrl, payload, this.getAuthHeaders()).subscribe({
      next: (response: any) => {
        this.isLoading = false;

        // Handle the API response structure with data property
        let responseData: any[] = [];
        if (response && response.data && Array.isArray(response.data)) {
          responseData = response.data;

          // Extract column definitions if available
          if (response.columnDef) {
            this.columnDefinitions = response.columnDef;
            this.orderedDisplayFields = this.getOrderedDisplayFields();
          }
        } else if (Array.isArray(response)) {
          responseData = response;
        }

        if (responseData.length > 0) {
          // Cache the full response for future client-side filtering
          this.apiCache[cacheKey] = responseData;

          // Filter the response based on search term using flexible search
          const filtered = responseData.filter(option => {
            return this.matchesSearchTerm(option, searchTerm);
          });

          this.filteredOptions = filtered;
          this.showDropdown = true;
        } else {
          this.setEmptyDropdownState();
        }
      },
      error: () => {
        this.isLoading = false;
        this.setEmptyDropdownState();
      }
    });
  }

  private setEmptyDropdownState(): void {
    this.filteredOptions = [];
    this.showDropdown = true;
  }

  private setDropdownValue(option: DropdownOption): void {
    // Mark that we're setting a dropdown value to prevent input conflicts
    this.settingDropdownValue = true;

    if (this.formControl) {
      // Get the display text for the input field
      const displayText = this.getOptionDisplayText(option);

      // Get the stored value using the dynamic method
      const storedValue = this.getStoredValue(option);
      this.formControl.setValue(storedValue);

      // Set the input element's display value to the human-readable text
      setTimeout(() => {
        const inputElement = document.getElementById(this.uniqueId) as HTMLInputElement;
        if (inputElement) {
          inputElement.value = displayText;
        }
      }, 0);

      // Force change detection and validation
      this.formControl.markAsDirty();
      this.formControl.markAsTouched();
      this.formControl.updateValueAndValidity();

      // Force Angular change detection
      this.cdr.detectChanges();
    }

    // Close dropdown
    this.showDropdown = false;

    // Emit value change event with unique identifier
    const displayText = this.getOptionDisplayText(option);
    const storedValue = this.getStoredValue(option);
    this.valueChange.emit({
      fieldName: this.uniqueId, // Use unique identifier instead of fieldName
      value: storedValue,
      option: option,
      displayText: displayText
    });

    // Clear the dropdown value setting flag after a short delay
    setTimeout(() => {
      this.settingDropdownValue = false;
    }, 100);
  }

  /**
   * Get the value that should be stored in the form control
   */
  private getStoredValue(option: DropdownOption): any {
    if (!option) return '';

    // If we have column definitions, use the first field (usually the ID/key field)
    if (this.orderedDisplayFields.length > 0) {
      const firstField = this.orderedDisplayFields[0];
      if (option[firstField] !== undefined && option[firstField] !== null) {
        return option[firstField];
      }
    }

    // Completely dynamic: use the first available field from the actual data
    const keys = Object.keys(option);
    if (keys.length > 0) {
      return option[keys[0]] || '';
    }

    return '';
  }

  /**
   * Get the display text for input field (uses first column from table)
   */
  public getOptionDisplayText(option: DropdownOption): string {
    if (!option) return '';

    // Use the first ordered field from column definitions
    if (this.orderedDisplayFields.length > 0) {
      const primaryField = this.orderedDisplayFields[0];
      if (option[primaryField]) {
        return option[primaryField].toString();
      }
    }

    // Fallback to first available field
    const keys = Object.keys(option).filter(key =>
      option[key] &&
      option[key].toString().trim() !== ''
    );
    if (keys.length > 0) {
      return option[keys[0]].toString();
    }

    return '';
  }

  /**
   * Get ordered display fields based on column definitions
   */
  private getOrderedDisplayFields(): string[] {
    if (!this.columnDefinitions || Object.keys(this.columnDefinitions).length === 0) {
      return [];
    }

    // Sort fields by column number
    return Object.keys(this.columnDefinitions)
      .sort((a, b) => {
        const colA = this.columnDefinitions[a].column || 999;
        const colB = this.columnDefinitions[b].column || 999;
        return colA - colB;
      });
  }

  /**
   * Check if an option matches the search term (completely dynamic)
   */
  private matchesSearchTerm(option: DropdownOption, searchTerm: string): boolean {
    if (!searchTerm || searchTerm.trim() === '') {
      return true;
    }

    const lowerSearchTerm = searchTerm.toLowerCase();

    // Search across all values - convert everything to string dynamically
    return Object.values(option).some(value => {
      if (value != null) { // Check for null and undefined
        return value.toString().toLowerCase().includes(lowerSearchTerm);
      }
      return false;
    });
  }



  private preloadDropdownData(): void {
    const cacheKey = this.getCacheKey();

    // Handle preloaded options
    if (this.options && this.options.length > 0) {
      this.apiCache[cacheKey] = this.options;
      return;
    }

    // Skip if already cached
    if (this.apiCache[cacheKey]) {
      return;
    }

    const apiUrl = this.getApiUrl();
    const payload = this.getApiPayload();

    if (!apiUrl) {
      return;
    }

    this.http.post<any[]>(apiUrl, payload, this.getAuthHeaders()).subscribe({
      next: (response: any) => {
        // Handle the API response structure with data property
        let responseData: any[] = [];
        if (response && response.data && Array.isArray(response.data)) {
          responseData = response.data;

          // Extract column definitions if available
          if (response.columnDef) {
            this.columnDefinitions = response.columnDef;
            this.orderedDisplayFields = this.getOrderedDisplayFields();
          }
        } else if (Array.isArray(response)) {
          responseData = response;
        }

        if (responseData.length > 0) {
          this.apiCache[cacheKey] = responseData;
        }
      },
      error: () => {
        // Handle preload error silently
      }
    });
  }

  // Utility methods for template
  getKeys(option: DropdownOption): string[] {
    return Object.keys(option);
  }

  // Performance optimization: trackBy functions
  trackByOptionId(_index: number, option: DropdownOption): string {
    // Use the same logic as getStoredValue for consistent tracking
    return this.getStoredValue(option)?.toString() || _index.toString();
  }

  trackByKey(_index: number, key: string): string {
    return key;
  }

  // Computed properties for template
  get inputClass(): string {
    // Start with base form-input class
    let classes = 'form-input';

    // Add any additional CSS classes passed in
    if (this.cssClass) {
      // If cssClass already contains 'form-input', don't duplicate it
      if (this.cssClass.includes('form-input')) {
        classes = this.cssClass;
      } else {
        classes += ` ${this.cssClass}`;
      }
    }

    // Add disabled state if needed
    if (this.isDisabled || this.isReadonly) {
      classes += ' disabled';
    }

    return classes;
  }

  get dropdownArrowIcon(): string {
    return this.showDropdown ? 'keyboard_arrow_up' : 'keyboard_arrow_down';
  }

  get emptyMessage(): string {
    return this.config.emptyMessage || 'No options found';
  }

  get placeholderText(): string {
    return this.config.placeholder || '';
  }

  get tooltipText(): string {
    return this.config.tooltip || 'Show options';
  }

  get dropdownMaxHeight(): string {
    return this.config.maxHeight || '200px';
  }
}
