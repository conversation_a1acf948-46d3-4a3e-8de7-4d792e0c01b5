# Flexible Dropdown Component Guide

## Overview

The dropdown component has been updated to be completely flexible and automatically adapt to any data structure returned by the API. No more hardcoded field names like 'ID' and 'Desc'!

## Key Changes

### 1. Automatic Field Detection
- The dropdown now automatically detects all available fields from the API response
- Uses the `columnDef` metadata from the API to determine field order and labels
- No need to specify which fields to select in the API payload

### 2. Completely Dynamic Value Storage
The dropdown automatically finds the best field to use as the stored value by:
- Using the first field from ordered column definitions (if available)
- Using the first available field from the actual data (completely dynamic)
- **NO MORE HARDCODED FIELD CHECKS** - adapts to any data structure

### 3. Completely Dynamic Display Text
The dropdown automatically determines what to display by:
- Using `config.primaryDisplayField` if specified
- Using the first field from ordered column definitions
- Using the first non-empty field from the actual data (completely dynamic)
- **NO MORE HARDCODED FIELD NAMES** - works with any field structure

### 4. Table Display System

#### Table Mode Only
The dropdown shows multiple columns in a table format using the `columnDef` metadata from the API response. This provides users with rich context to make informed selections.

**The system expects APIs to provide proper `columnDef` metadata with multiple fields for optimal user experience.**

## API Response Structure

The component expects this response structure:

```json
{
  "data": [
    {
      "ID": "AF",
      "countryName": "Afghanistan",
      "countryCode": "+93",
      "currency": "AFN"
    }
  ],
  "columnDef": {
    "countryName": {
      "label": "Country Name",
      "column": 1
    },
    "ID": {
      "label": "Code",
      "column": 2
    },
    "countryCode": {
      "label": "Phone Code",
      "column": 3
    },
    "currency": {
      "label": "Currency",
      "column": 4
    }
  }
}
```

## Configuration Options

### New Options

- `displayMode?: 'single' | 'table'` - Controls how options are displayed
- `primaryDisplayField?: string` - Specifies which field to use for single display mode

### Updated API Payload

The component now sends a simplified payload:

```json
{
  "_search": "search_term",  // Only when searching
  "_limit": 50
}
```

No more hardcoded `_select` fields - the API returns all available fields.

## Migration Guide

### Before (Hardcoded)
```typescript
// Old way - hardcoded type mappings and field checks
switch (this.config.type) {
  case 'type': return 'fieldType';
  case 'foreignKey': return 'formDefinition';
  case 'regular':
    const field = this.fields.find(f => f.fieldName === originalFieldName);
    return field?.foreginKey || 'unknown';
}

const displayFields = ['name', 'title', 'description', 'label', 'text', 'value', 'Desc', 'ID'];
const idFields = ['ID', 'id', 'Id', 'value', 'key'];
```

### After (Completely Dynamic)
```typescript
// New way - completely dynamic using queryBuilderId
private getCacheKey(): string {
  return this.config.queryBuilderId || this.fieldName || this.config.type || 'default';
}

// Dynamic field detection from actual data
const keys = Object.keys(option).filter(key =>
  option[key] && option[key].toString().trim() !== ''
);

// Dynamic search across all fields
return Object.values(option).some(value =>
  value && value.toString().toLowerCase().includes(searchTerm.toLowerCase())
);
```

## Usage Examples

### API-Based Dropdown
```typescript
const dropdownConfig: DropdownConfig = {
  queryBuilderId: 'country', // API endpoint identifier
  placeholder: 'Select option'
  // Automatically displays table format using columnDef metadata
};
```

### Preloaded Options Dropdown
```typescript
const dropdownConfig: DropdownConfig = {
  options: [
    { id: 1, name: 'Option 1' },
    { id: 2, name: 'Option 2' }
  ],
  placeholder: 'Select option'
  // Uses client-side filtering with preloaded data
};
```

**Note:** For API-based dropdowns, the API must provide `columnDef` metadata for proper table display.

## Benefits

1. **No More Hardcoding**: Works with any data structure automatically
2. **Better UX**: Table mode shows more information to users
3. **Flexible**: Adapts to different API responses without code changes
4. **Maintainable**: Less code to maintain, fewer bugs
5. **Future-Proof**: Will work with new data structures without modifications

## Demo Component

See `flexible-dropdown-demo.component.ts` for a complete working example with both single and table display modes.
