<!-- Regular Field (non-grouped, non-multi) - EXACT from main component lines 54-198 - FormGroup Added -->
<div class="form-field" [formGroup]="form">

  <!-- NEW: Language Field Support (completely non-invasive addition) -->
  @if (field.language === true) {
    <app-language-field
      [field]="field"
      [form]="form"
      [isViewMode]="isViewMode"
      (fieldValueChange)="fieldValueChange.emit($event)">
    </app-language-field>
  } @else {
    <!-- EXISTING LOGIC: All original field rendering logic remains unchanged -->
    <label [for]="field.fieldName">{{ field.label || field.fieldName }} @if (field.mandatory) {<span>*</span>}
@if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
 </label>

    @if (hasDropdownData(field)) {
      @if (hasMultiColumnData(field)) {
         <app-multi-column-dropdown
          [fieldName]="field.fieldName"
          [formControl]="getFormControl(field.fieldName)"
          [config]="getMultiColumnDropdownConfig(field)"
          [isDisabled]="isViewMode || field.noInput"
          [isReadonly]="isViewMode || field.noInput"
          [inputId]="getUniqueFieldId(field.fieldName)"
          (valueChange)="onMultiColumnValueChange($event)">
        </app-multi-column-dropdown>
      } @else {
         <app-dropdown
          [fieldName]="field.fieldName"
          [formControl]="getFormControl(field.fieldName)"
          [config]="getDynamicDropdownConfig(field)"
          [isDisabled]="isViewMode || field.noInput"
          [isReadonly]="isViewMode || field.noInput"
          [fields]="fields"
          [inputId]="getUniqueFieldId(field.fieldName)"
          [options]="getFieldOptions(field)"
          (valueChange)="onDropdownValueChange($event)">
        </app-dropdown>
      }
    } @else {
      <!-- Dynamic input field based on detected field type -->
      <input
        class="form-input"
        [formControlName]="field.fieldName"
        [id]="field.fieldName"
        [type]="getDynamicInputType(field)"
        [step]="getDynamicInputStep(field)"
        [readonly]="isViewMode || field.noInput"
        [placeholder]="(field.label?.trim() || field.fieldName)" />
    }
  } <!-- End of @else block for non-language fields -->
</div>
