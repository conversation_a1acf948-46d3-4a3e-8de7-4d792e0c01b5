# How to Integrate Multi-Column Dropdown with Dynamic Query

## 🎯 **Current Status: View List Button Working!**

The "View List" button is now working and will show an alert when clicked. Here's how to complete the integration:

## **Step 1: Test the Current Implementation**

1. **Open any page** with a multi-column dropdown that has more than 6 columns
2. **Click the blue "View List" button** at the bottom of the dropdown
3. **You should see an alert** with details about the dynamic query integration
4. **Check the console** for detailed configuration logs

## **Step 2: Add Event Listener to Your Parent Component**

### **In your parent component TypeScript file:**

```typescript
import { DynamicQueryEvent } from './path/to/multi-column-dropdown.component';

export class YourParentComponent {
  
  // Handle dynamic query events
  onDynamicQueryEvent(event: DynamicQueryEvent) {
    console.log('Dynamic Query Event:', event);
    
    if (event.type === 'OPEN_QUERY' && event.config) {
      this.openDynamicQueryModal(event.config);
    }
  }

  // Open the dynamic query component
  private openDynamicQueryModal(config: any) {
    console.log('Opening Dynamic Query with config:', config);
    
    // Option 1: Open as Modal Dialog
    // this.dialog.open(DynamicQueryComponent, {
    //   data: config,
    //   width: '90%',
    //   height: '80%'
    // });
    
    // Option 2: Navigate to Dynamic Query Page
    // this.router.navigate(['/dynamic-query'], {
    //   queryParams: { config: JSON.stringify(config) }
    // });
    
    // Option 3: Show in Sidebar/Panel
    // this.showDynamicQueryPanel = true;
    // this.dynamicQueryConfig = config;
    
    // For now, just show the config
    alert(`Dynamic Query Config:\n${JSON.stringify(config, null, 2)}`);
  }
}
```

### **In your parent component template:**

```html
<app-multi-column-dropdown
  [formControl]="yourFormControl"
  [config]="yourDropdownConfig"
  fieldName="yourFieldName"
  (dynamicQueryEvent)="onDynamicQueryEvent($event)">
</app-multi-column-dropdown>
```

## **Step 3: Import and Use Dynamic Query Component**

### **Import the Dynamic Query Component:**

```typescript
import { DynamicQueryComponent } from '../../../dynamic-query/dynamic-query.component';
```

### **Configure the Dynamic Query Component:**

```typescript
private openDynamicQueryModal(config: any) {
  // Create component dynamically
  const componentRef = this.viewContainer.createComponent(DynamicQueryComponent);
  
  // Configure the component
  componentRef.instance.queryName = config.queryBuilderId;
  
  // Set up any additional configuration
  // componentRef.instance.columns = config.columns;
  // componentRef.instance.title = config.title;
  
  // Handle results
  // componentRef.instance.resultsSelected.subscribe((results) => {
  //   this.handleDynamicQueryResults(results);
  //   componentRef.destroy();
  // });
}
```

## **Step 4: Handle Dynamic Query Results**

```typescript
private handleDynamicQueryResults(results: any[]) {
  console.log('Dynamic Query Results:', results);
  
  if (results && results.length > 0) {
    // Set the selected value in your form control
    this.yourFormControl.setValue(results[0]);
    
    // Or handle multiple selections
    // this.yourFormControl.setValue(results);
  }
}
```

## **Step 5: Example Complete Integration**

```typescript
import { Component, ViewContainerRef } from '@angular/core';
import { FormControl } from '@angular/forms';
import { DynamicQueryComponent } from '../../../dynamic-query/dynamic-query.component';
import { DynamicQueryEvent } from './path/to/multi-column-dropdown.component';

@Component({
  selector: 'app-your-component',
  template: `
    <div>
      <label>Select Country:</label>
      <app-multi-column-dropdown
        [formControl]="countryControl"
        [config]="countryConfig"
        fieldName="country"
        (dynamicQueryEvent)="onDynamicQueryEvent($event)">
      </app-multi-column-dropdown>
      
      <div>Selected: {{ countryControl.value | json }}</div>
    </div>
  `
})
export class YourComponent {
  
  countryControl = new FormControl('');

  countryConfig = {
    queryBuilderId: this.getDynamicQueryBuilderId(),
    placeholder: 'Select Country',
    emptyMessage: 'No countries found'
  };

  // Dynamic method to determine queryBuilderId from field configuration
  getDynamicQueryBuilderId(): string {
    // In real implementation, this would come from field configuration
    const fieldConfig = {
      fieldName: 'country',
      dataSource: 'country,new',
      endpoint: 'country_master'
    };

    // Use dynamic property scanning
    const allProperties = Object.keys(fieldConfig);
    for (const prop of allProperties) {
      const value = fieldConfig[prop as keyof typeof fieldConfig];
      if (value && typeof value === 'string' && value.trim() !== '') {
        return value;
      }
    }
    return 'default';
  }

  constructor(private viewContainer: ViewContainerRef) {}

  onDynamicQueryEvent(event: DynamicQueryEvent) {
    if (event.type === 'OPEN_QUERY') {
      this.openDynamicQuery(event.config);
    }
  }

  private openDynamicQuery(config: any) {
    // Your implementation here
    console.log('Open Dynamic Query:', config);
    
    // Example: Create and configure dynamic query component
    const queryComponent = this.viewContainer.createComponent(DynamicQueryComponent);
    queryComponent.instance.queryName = config.queryBuilderId;
  }
}
```

## **🎯 What You Should See Now:**

1. **Alert popup** when clicking "View List" button
2. **Console logs** with complete dynamic query configuration
3. **Event emission** working properly
4. **All column metadata** available for dynamic query

## **🚀 Next Steps:**

1. **Test the alert** - Click "View List" to confirm it's working
2. **Add event listener** to your parent component
3. **Import dynamic query component** from `src/app/dynamic-query/`
4. **Configure and open** the dynamic query component
5. **Handle results** and update your form control

**The integration is ready - you just need to connect it to your dynamic query component!** 🎯🔍✨
