import { Component, Input, Output, EventEmitter, On<PERSON><PERSON>roy, OnInit, OnC<PERSON><PERSON>, inject, ChangeDetectorRef } from '@angular/core';
import { FormGroup, FormArray, FormControl, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DropdownComponent, DropdownConfig, DropdownValueChangeEvent } from '../dropdown/dropdown.component';
import { MultiColumnDropdownComponent, MultiColumnDropdownConfig, MultiColumnValueChangeEvent } from '../multi-column-dropdown/multi-column-dropdown.component';
import { LanguageFieldComponent } from '../language-field/language-field.component';

@Component({
  selector: 'app-regular-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule,
    DropdownComponent,
    MultiColumnDropdownComponent,
    LanguageFieldComponent
  ],
  templateUrl: './regular-field.component.html',
  styleUrl: './regular-field.component.scss'
})
export class RegularFieldComponent implements OnInit, OnDestroy, OnChanges {
  @Input() field!: any;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() fields: any[] = []; // Need access to all fields for extractOriginalFieldName
  @Input() multiIndex?: number; // Optional index for multi-fields (1-based)
  @Input() groupIndex?: number; // Optional group index for grouped fields
  @Input() nestedGroupIndex?: number; // Optional nested group index for nested groups

  @Output() fieldValueChange = new EventEmitter<{fieldName: string, value: any}>();

  // Note: Dropdown properties moved to unified DropdownComponent
  // API caching is now handled by the unified DropdownComponent

  private cdr = inject(ChangeDetectorRef);

  ngOnInit() {
    // Dropdown preloading is now handled by the unified DropdownComponent
    // Ensure form control is properly disabled when it should be
    this.updateFormControlDisabledState();
    
    // Ensure form control exists for this field
    this.ensureFormControlExists();
  }

  /**
   * Ensure form control exists for this field
   */
  private ensureFormControlExists(): void {
    if (!this.form || !this.field) return;
    
    const formControl = this.form.get(this.field.fieldName);
    if (!formControl) {
      // Try to create the form control if it doesn't exist
      const validators = this.field.mandatory ? Validators.required : null;
      const control = new FormControl(this.getDynamicDefaultValue(this.field), validators);
      this.form.addControl(this.field.fieldName, control);
      
      // Disable control if noInput is true
      if (this.field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
  }

  private updateFormControlDisabledState(): void {
    const formControl = this.form.get(this.field.fieldName);
    if (formControl) {
      if (this.isViewMode || this.field.noInput) {
        if (formControl.enabled) {
          formControl.disable({ emitEvent: false });
        }
      } else {
        if (formControl.disabled) {
          formControl.enable({ emitEvent: false });
        }
      }
    }
  }

  ngOnChanges(): void {
    // Update form control disabled state when inputs change
    this.updateFormControlDisabledState();
  }

  ngOnDestroy() {
    // Cleanup handled by unified DropdownComponent
  }

  // Note: All dropdown methods moved to unified DropdownComponent
  // Utility methods are now handled by the unified DropdownComponent

  // Note: Group path parsing and array access handled by parent component

  // Dropdown preloading is now handled by the unified DropdownComponent

  // Completely dynamic dropdown configuration methods
  getTypeDropdownConfig(field: any): DropdownConfig {
    return {
      queryBuilderId: this.getQueryBuilderId(field, 'type'),
      searchEnabled: true,
      placeholder: `Search ${field.label?.trim() || field.fieldName}`,
      emptyMessage: 'No types found',
      tooltip: 'Show type suggestions'
    };
  }

  getForeignKeyDropdownConfig(field: any): DropdownConfig {
    return {
      queryBuilderId: this.getQueryBuilderId(field, 'foreignKey'),
      searchEnabled: true,
      placeholder: `Search ${field.label?.trim() || field.fieldName}`,
      emptyMessage: 'No foreign keys found',
      tooltip: 'Show foreign key suggestions'
    };
  }

  getRegularDropdownConfig(field: any): DropdownConfig {
    return {
      queryBuilderId: this.getQueryBuilderId(field, 'regular'),
      searchEnabled: true,
      placeholder: `Search ${field.label?.trim() || field.fieldName}`,
      emptyMessage: 'No options found',
      tooltip: 'Show options'
    };
  }

  getLookupDropdownConfig(field: any): DropdownConfig {
    return {
      options: field.lookupOptions || [],
      queryBuilderId: this.getQueryBuilderId(field, 'lookup'),
      searchEnabled: true,
      placeholder: `Select ${field.label?.trim() || field.fieldName}`,
      emptyMessage: 'No options found',
      tooltip: 'Show lookup options'
    };
  }

  // Completely dynamic method - scans ALL field properties without any assumptions
  private getQueryBuilderId(field: any, context: string): string {
    if (!field) return 'default';

    // Completely dynamic: scan ALL properties and use the first non-empty string value
    const allProperties = Object.keys(field);

    for (const prop of allProperties) {
      const value = field[prop];
      // Use any string property that has a meaningful value
      if (value && typeof value === 'string' && value.trim() !== '') {
        return value;
      }
    }

    // If no string property found, use 'default'
    return 'default';
  }

  // Dynamic method to check if field has any dropdown-related data
  hasDropdownData(field: any): boolean {
    if (!field) return false;

    // Check if field has any properties that could indicate dropdown data
    const allProperties = Object.keys(field);

    return allProperties.some(prop => {
      const value = field[prop];
      return (
        (typeof value === 'string' && value.trim() !== '') ||
        (Array.isArray(value) && value.length > 0) ||
        (typeof value === 'object' && value !== null)
      );
    });
  }

  // Dynamic method to check if field should use multi-column dropdown
  hasMultiColumnData(field: any): boolean {
    if (!field) return false;

    // Look for any property that suggests multi-column data structure
    const allProperties = Object.keys(field);

    return allProperties.some(prop => {
      const value = field[prop];
      // Check for properties that typically indicate multi-column data
      return (
        typeof value === 'string' &&
        value.trim() !== '' &&
        prop !== 'fieldName' &&
        prop !== 'label'
      );
    });
  }

  // Dynamic dropdown configuration - uses first available query identifier
  getDynamicDropdownConfig(field: any): DropdownConfig {
    return {
      queryBuilderId: this.getQueryBuilderId(field, 'dynamic'),
      options: this.getFieldOptions(field),
      searchEnabled: true,
      placeholder: `Search ${field.label?.trim() || field.fieldName}`,
      emptyMessage: 'No options found',
      tooltip: 'Show options'
    };
  }

  // Dynamic method to get field options from any array property
  getFieldOptions(field: any): any[] {
    if (!field) return [];

    // Look for any array property that could contain options
    const allProperties = Object.keys(field);

    for (const prop of allProperties) {
      const value = field[prop];
      if (Array.isArray(value) && value.length > 0) {
        return value;
      }
    }

    return [];
  }

  // Dynamic method to determine input type from field data
  getDynamicInputType(field: any): string {
    if (!field) return 'text';

    // Scan all field properties to infer the best input type
    const allProperties = Object.keys(field);

    for (const prop of allProperties) {
      const value = field[prop];

      // Check for any property that suggests the input type
      if (typeof value === 'boolean') {
        return 'checkbox';
      }
      if (typeof value === 'number') {
        return 'number';
      }
      if (value instanceof Date) {
        return 'date';
      }
      if (typeof value === 'string') {
        // Check if string looks like a date
        const dateRegex = /^\d{4}-\d{2}-\d{2}|^\d{2}\/\d{2}\/\d{4}/;
        if (dateRegex.test(value)) {
          return 'date';
        }
        // Check if string looks like a number
        if (!isNaN(Number(value)) && value.trim() !== '') {
          return 'number';
        }
      }
    }

    // Default to text for any unknown type
    return 'text';
  }

  // Dynamic method to determine input step for number inputs
  getDynamicInputStep(field: any): string | null {
    if (!field) return null;

    // Check if any field property suggests decimal precision
    const allProperties = Object.keys(field);

    for (const prop of allProperties) {
      const value = field[prop];

      if (typeof value === 'number' && value % 1 !== 0) {
        // Has decimal places, use step for decimals
        return '0.01';
      }
      if (typeof value === 'string' && value.includes('.')) {
        // String with decimal point
        return '0.01';
      }
    }

    // No step needed for integers or non-numeric fields
    return null;
  }

  // Dynamic method to determine default value for form control
  getDynamicDefaultValue(field: any): any {
    if (!field) return '';

    // Scan all field properties to determine appropriate default value
    const allProperties = Object.keys(field);

    for (const prop of allProperties) {
      const value = field[prop];

      // Use the actual data type to determine default
      if (typeof value === 'boolean') {
        return false;
      }
      if (typeof value === 'number') {
        return 0;
      }
      if (value instanceof Date) {
        return null;
      }
      if (typeof value === 'string') {
        // Check if string looks like a date
        const dateRegex = /^\d{4}-\d{2}-\d{2}|^\d{2}\/\d{2}\/\d{4}/;
        if (dateRegex.test(value)) {
          return null;
        }
        // Check if string looks like a number
        if (!isNaN(Number(value)) && value.trim() !== '') {
          return 0;
        }
      }
    }

    // Default to empty string for any unknown type
    return '';
  }

  // Multi-column dropdown configuration for foreign key fields
  getMultiColumnDropdownConfig(field: any): MultiColumnDropdownConfig {
    return {
      queryBuilderId: this.getQueryBuilderId(field, 'multiColumn'),
      placeholder: `Search ${field.label?.trim() || field.fieldName}`,
      emptyMessage: 'No options found',
      tooltip: 'Show options',
      limit: 50,
      searchDebounceTime: 300,
      showTableHeaders: true
    };
  }

  // Event handler for unified dropdown component
  onDropdownValueChange(event: DropdownValueChangeEvent): void {
    // Map the unique ID back to the original field name
    const originalFieldName = this.extractOriginalFieldName(event.fieldName);

    // Emit the field value change event for parent component
    this.fieldValueChange.emit({
      fieldName: originalFieldName,
      value: event.value
    });
  }

  // Event handler for multi-column dropdown component
  onMultiColumnValueChange(event: MultiColumnValueChangeEvent): void {
    // Map the unique ID back to the original field name
    const originalFieldName = this.extractOriginalFieldName(event.fieldName);

    // Emit the field value change event for parent component
    this.fieldValueChange.emit({
      fieldName: originalFieldName,
      value: event.value
    });
  }

  // Extract original field name from unique ID
  private extractOriginalFieldName(uniqueId: string): string {
    // Handle complex unique IDs with group, nested, and multi indices
    if (uniqueId.includes('_group_') || uniqueId.includes('_nested_') || uniqueId.includes('_multi_')) {
      // Split by underscores and take the first part (original field name)
      const parts = uniqueId.split('_');
      return parts[0];
    }
    return uniqueId;
  }

  // Helper method to get FormControl with proper typing
  getFormControl(fieldName: string): any {
    return this.form.get(fieldName);
  }

  // Generate unique ID for dropdown fields to prevent conflicts in multi-field scenarios
  getUniqueFieldId(fieldName: string): string {
    let uniqueId = fieldName;
    
    // Add group index if available
    if (this.groupIndex !== undefined) {
      uniqueId += `_group_${this.groupIndex}`;
    }
    
    // Add nested group index if available
    if (this.nestedGroupIndex !== undefined) {
      uniqueId += `_nested_${this.nestedGroupIndex}`;
    }
    
    // Add multi index if available
    if (this.multiIndex) {
      uniqueId += `_multi_${this.multiIndex}`;
    }
    
    return uniqueId;
  }
}
