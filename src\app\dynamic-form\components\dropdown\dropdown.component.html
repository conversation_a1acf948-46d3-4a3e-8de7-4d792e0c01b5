<!-- Unified Dropdown Component Template -->
<div class="dropdown-input-container" [class.disabled]="isDisabled || isReadonly">
  <!-- Input field with search capability -->
  <input 
    [id]="uniqueId"
    [formControl]="formControl"
    [class]="inputClass"
    [placeholder]="placeholderText"
    [disabled]="isDisabled"
    [readonly]="isReadonly"
    type="text"
    autocomplete="off"
    (input)="onInputChange($event)"
    (focus)="onInputFocus()"
    (blur)="onInputBlur()" />
  
  <!-- Arrow button to toggle dropdown -->
  @if (showArrowButton) {
    <button 
      type="button" 
      class="dropdown-arrow-btn" 
      (click)="toggleDropdown()" 
      [disabled]="isDisabled || isReadonly"
      [matTooltip]="tooltipText">
      <mat-icon>{{ dropdownArrowIcon }}</mat-icon>
    </button>
  }
  
  <!-- Dropdown list for filtered results -->
  @if (showDropdown) {
    <div class="dropdown-list" [style.max-height]="dropdownMaxHeight">
      <!-- Loading state -->
      @if (isLoading) {
        <div class="dropdown-loading">
          <mat-icon>refresh</mat-icon>
          Loading...
        </div>
      }
      
      <!-- Options list -->
      @else if (filteredOptions && filteredOptions.length > 0) {
        @for (option of filteredOptions; track trackByOptionId($index, option)) {
          <div class="dropdown-item" (click)="selectOption(option)">
            <!-- Table display: show multiple columns using columnDef metadata -->
            <div class="dropdown-item-table">
              @for (field of orderedDisplayFields; track trackByKey($index, field)) {
                <div class="dropdown-item-column"
                     [attr.data-label]="columnDefinitions[field]?.label || field">
                  {{ option[field] || '' }}
                </div>
              }
            </div>
          </div>
        }
      }
      
      <!-- Empty state -->
      @else {
        <div class="dropdown-empty">
          {{ emptyMessage }}
        </div>
      }
    </div>
  }
</div>
