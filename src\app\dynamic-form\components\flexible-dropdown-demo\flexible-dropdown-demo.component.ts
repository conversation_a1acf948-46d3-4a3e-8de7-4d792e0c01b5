import { Component, OnInit } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { DropdownComponent, DropdownConfig, DropdownValueChangeEvent } from '../dropdown/dropdown.component';

@Component({
  selector: 'app-flexible-dropdown-demo',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DropdownComponent
  ],
  template: `
    <div style="padding: 20px; max-width: 800px; margin: 0 auto;">
      <h2>Flexible Dropdown Demo</h2>
      <p>This demo shows how the dropdown automatically adapts to any data structure using columnDef metadata.</p>
      
      <div style="margin: 20px 0;">
        <label style="display: block; margin-bottom: 8px; font-weight: 500;">
          Country Dropdown (Single Display Mode):
        </label>
        
        <app-dropdown
          [formControl]="countryControlSingle"
          [config]="countryConfigSingle"
          fieldName="country"
          (valueChange)="onValueChange($event)"
          style="display: block; width: 100%; max-width: 400px;">
        </app-dropdown>
      </div>

      <div style="margin: 20px 0;">
        <label style="display: block; margin-bottom: 8px; font-weight: 500;">
          Country Dropdown (Table Display Mode):
        </label>
        
        <app-dropdown
          [formControl]="countryControlTable"
          [config]="countryConfigTable"
          fieldName="countryTable"
          (valueChange)="onValueChange($event)"
          style="display: block; width: 100%; max-width: 600px;">
        </app-dropdown>
      </div>

      <div style="margin: 20px 0;">
        <h3>Selected Values:</h3>
        <div style="background: #f5f5f5; padding: 10px; border-radius: 4px;">
          <p><strong>Single Mode:</strong> {{ countryControlSingle.value }}</p>
          <p><strong>Table Mode:</strong> {{ countryControlTable.value }}</p>
        </div>
      </div>

      <div style="margin: 20px 0;">
        <h3>Last Value Change Event:</h3>
        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 12px;">{{ lastEvent | json }}</pre>
      </div>

      <div style="margin: 20px 0;">
        <h3>How It Works:</h3>
        <ul style="line-height: 1.6;">
          <li><strong>Automatic Field Detection:</strong> The dropdown automatically detects available fields from the API response</li>
          <li><strong>Column Definitions:</strong> Uses the <code>columnDef</code> metadata to determine field order and labels</li>
          <li><strong>Flexible Value Storage:</strong> Automatically finds the best field to use as the stored value (ID, id, value, etc.)</li>
          <li><strong>Display Modes:</strong> 
            <ul>
              <li><code>single</code> - Shows only the primary display field</li>
              <li><code>table</code> - Shows multiple columns in a table format</li>
            </ul>
          </li>
          <li><strong>No Hardcoded Fields:</strong> No more checking for specific field names like 'ID' or 'Desc'</li>
        </ul>
      </div>
    </div>
  `,
  styles: [`
    :host {
      display: block;
    }
    
    ul {
      margin: 10px 0;
      padding-left: 20px;
    }
    
    li {
      margin: 5px 0;
    }
    
    code {
      background: #e9ecef;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
    }
  `]
})
export class FlexibleDropdownDemoComponent implements OnInit {
  
  countryControlSingle = new FormControl('');
  countryControlTable = new FormControl('');
  lastEvent: DropdownValueChangeEvent | null = null;

  // Single display mode configuration
  countryConfigSingle: DropdownConfig = {
    type: 'regular',
    queryBuilderId: 'country',
    searchEnabled: true,
    placeholder: 'Select Country',
    emptyMessage: 'No countries found',
    tooltip: 'Search and select a country',
    maxHeight: '300px',
    limit: 50,
    displayMode: 'single',
    primaryDisplayField: 'countryName' // Specify which field to show in single mode
  };

  // Table display mode configuration
  countryConfigTable: DropdownConfig = {
    type: 'regular',
    queryBuilderId: 'country',
    searchEnabled: true,
    placeholder: 'Select Country (Table View)',
    emptyMessage: 'No countries found',
    tooltip: 'Search and select a country',
    maxHeight: '300px',
    limit: 50,
    displayMode: 'table' // Show multiple columns
  };

  ngOnInit() {
    console.log('Flexible Dropdown Demo initialized');
  }

  onValueChange(event: DropdownValueChangeEvent) {
    console.log('Dropdown value changed:', event);
    this.lastEvent = event;
  }
}
