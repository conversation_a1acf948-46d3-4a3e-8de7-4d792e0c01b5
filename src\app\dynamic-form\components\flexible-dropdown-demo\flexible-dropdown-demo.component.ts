import { Component, OnInit } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { DropdownComponent, DropdownConfig, DropdownValueChangeEvent } from '../dropdown/dropdown.component';

@Component({
  selector: 'app-flexible-dropdown-demo',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DropdownComponent
  ],
  template: `
    <div style="padding: 20px; max-width: 800px; margin: 0 auto;">
      <h2>Table Mode Dropdown Demo</h2>
      <p>This demo shows the dropdown displaying data in table format using columnDef metadata from the API.</p>

      <div style="margin: 20px 0;">
        <label style="display: block; margin-bottom: 8px; font-weight: 500;">
          Country Dropdown (Table Display):
        </label>

        <app-dropdown
          [formControl]="countryControl"
          [config]="countryConfig"
          fieldName="country"
          (valueChange)="onValueChange($event)"
          style="display: block; width: 100%; max-width: 600px;">
        </app-dropdown>
      </div>

      <div style="margin: 20px 0;">
        <h3>Selected Value:</h3>
        <div style="background: #f5f5f5; padding: 10px; border-radius: 4px;">
          <p><strong>Selected:</strong> {{ countryControl.value }}</p>
        </div>
      </div>

      <div style="margin: 20px 0;">
        <h3>Last Value Change Event:</h3>
        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 12px;">{{ lastEvent | json }}</pre>
      </div>

      <div style="margin: 20px 0;">
        <h3>How It Works:</h3>
        <ul style="line-height: 1.6;">
          <li><strong>Automatic Field Detection:</strong> Detects all available fields from the API response</li>
          <li><strong>Column Definitions:</strong> Uses <code>columnDef</code> metadata to determine field order and labels</li>
          <li><strong>Table Display:</strong> Shows multiple columns in organized table format</li>
          <li><strong>Flexible Value Storage:</strong> Automatically finds the best field to use as the stored value</li>
          <li><strong>No Hardcoded Fields:</strong> Completely dynamic - adapts to any data structure</li>
        </ul>
      </div>
    </div>
  `,
  styles: [`
    :host {
      display: block;
    }
    
    ul {
      margin: 10px 0;
      padding-left: 20px;
    }
    
    li {
      margin: 5px 0;
    }
    
    code {
      background: #e9ecef;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
    }
  `]
})
export class FlexibleDropdownDemoComponent implements OnInit {

  countryControl = new FormControl('');
  lastEvent: DropdownValueChangeEvent | null = null;

  // Dynamic dropdown configuration - queryBuilderId determined from field data
  countryConfig: DropdownConfig = {
    queryBuilderId: this.getDemoQueryBuilderId(),
    searchEnabled: true,
    placeholder: 'Select Country',
    emptyMessage: 'No countries found',
    tooltip: 'Search and select a country',
    maxHeight: '300px',
    limit: 50
    // Automatically displays table format using columnDef metadata
  };

  ngOnInit() {
    console.log('Flexible Dropdown Demo initialized');
  }

  onValueChange(event: DropdownValueChangeEvent) {
    console.log('Dropdown value changed:', event);
    this.lastEvent = event;
  }

  // Dynamic method to determine queryBuilderId (for demo purposes)
  getDemoQueryBuilderId(): string {
    // In a real application, this would come from field configuration
    // For demo, we simulate dynamic detection
    const demoField = {
      fieldName: 'country',
      dataSource: 'country',
      apiEndpoint: 'country_lookup'
    };

    // Use the same dynamic logic as the real component
    const allProperties = Object.keys(demoField);

    for (const prop of allProperties) {
      const value = demoField[prop as keyof typeof demoField];
      if (value && typeof value === 'string' && value.trim() !== '') {
        return value;
      }
    }

    return 'default';
  }
}
