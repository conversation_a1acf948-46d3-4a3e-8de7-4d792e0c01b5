import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MultiColumnDropdownComponent, DynamicQueryEvent } from '../multi-column-dropdown/multi-column-dropdown.component';

@Component({
  selector: 'app-multi-column-dropdown-demo',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MultiColumnDropdownComponent
  ],
  template: `
    <div style="padding: 20px; max-width: 800px; margin: 0 auto;">
      <h2>Multi-Column Dropdown Demo</h2>
      
      <div style="margin: 20px 0;">
        <label style="display: block; margin-bottom: 8px; font-weight: 500;">
          Country Dropdown (8 columns - shows 6, View List for all):
        </label>
        
        <app-multi-column-dropdown
          [formControl]="countryControl"
          [config]="countryConfig"
          fieldName="country"
          (dynamicQueryEvent)="onDynamicQueryEvent($event)"
          style="display: block; width: 100%; max-width: 400px;">
        </app-multi-column-dropdown>
      </div>

      <div style="margin: 20px 0;">
        <h3>Selected Value:</h3>
        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ countryControl.value | json }}</pre>
      </div>

      <div style="margin: 20px 0;">
        <h3>Last Dynamic Query Event:</h3>
        <pre style="background: #f0f8ff; padding: 10px; border-radius: 4px; max-height: 300px; overflow: auto;">{{ lastEvent | json }}</pre>
      </div>

      <!-- Dynamic Query Popup Simulation -->
      @if (showDynamicQueryPopup) {
        <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
          <div style="background: white; padding: 20px; border-radius: 8px; max-width: 90%; max-height: 90%; overflow: auto; box-shadow: 0 10px 25px rgba(0,0,0,0.2);">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
              <h3 style="margin: 0;">{{ dynamicQueryConfig?.title || 'Advanced Search' }}</h3>
              <button 
                (click)="closeDynamicQuery()"
                style="background: #dc3545; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer;">
                Close
              </button>
            </div>
            
            <p style="color: #666; margin-bottom: 20px;">{{ dynamicQueryConfig?.description }}</p>
            
            <div style="margin-bottom: 20px;">
              <h4>Available Columns ({{ dynamicQueryConfig?.columns?.length || 0 }}):</h4>
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                @for (column of dynamicQueryConfig?.columns || []; track column.fieldName) {
                  <div style="background: #f8f9fa; padding: 8px; border-radius: 4px; border-left: 3px solid #007bff;">
                    <strong>{{ column.label }}</strong><br>
                    <small style="color: #666;">{{ column.fieldName }} ({{ column.dataType }})</small>
                  </div>
                }
              </div>
            </div>
            
            <div style="background: #e7f3ff; padding: 15px; border-radius: 4px; border-left: 4px solid #007bff;">
              <strong>🎯 Integration Point:</strong><br>
              This popup demonstrates where you would integrate the dynamic query component from 
              <code>src/app/dynamic-query/</code>. The component would receive the configuration 
              and allow users to search and filter across all {{ dynamicQueryConfig?.columns?.length || 0 }} columns.
            </div>
          </div>
        </div>
      }
    </div>
  `,
  styles: [`
    :host {
      display: block;
    }
    
    pre {
      font-size: 12px;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
    
    code {
      background: #f1f1f1;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: monospace;
    }
  `]
})
export class MultiColumnDropdownDemoComponent implements OnInit {
  
  countryControl = new FormControl('');
  lastEvent: any = null;
  showDynamicQueryPopup = false;
  dynamicQueryConfig: any = null;

  countryConfig = {
    queryBuilderId: this.getDemoQueryBuilderId(),
    placeholder: 'Select Country',
    emptyMessage: 'No countries found',
    tooltip: 'Search and select a country',
    maxHeight: '400px',
    limit: 20,
    searchDebounceTime: 300,
    showTableHeaders: true
  };

  ngOnInit() {
    console.log('Multi-Column Dropdown Demo initialized');
  }

  onDynamicQueryEvent(event: DynamicQueryEvent) {
    console.log('🎯 Dynamic Query Event Received in Demo Component:', event);
    
    this.lastEvent = event;
    
    if (event.type === 'OPEN_QUERY' && event.config) {
      console.log('📊 Opening Dynamic Query with config:', event.config);
      
      // Store the config and show the popup
      this.dynamicQueryConfig = event.config;
      this.showDynamicQueryPopup = true;
      
      // In a real implementation, you would:
      // 1. Open the dynamic query component from src/app/dynamic-query/
      // 2. Pass the event.config to configure the query builder
      // 3. Handle the results when user selects items
      
      console.log('🔍 Available columns for search:', event.config.columns?.map(c => c.fieldName));
    }
  }

  closeDynamicQuery() {
    this.showDynamicQueryPopup = false;
    this.dynamicQueryConfig = null;

    console.log('❌ Dynamic Query popup closed');
  }

  // Dynamic method to determine queryBuilderId (for demo purposes)
  getDemoQueryBuilderId(): string {
    // In a real application, this would come from field configuration
    // For demo, we simulate dynamic detection
    const demoField = {
      fieldName: 'country',
      endpoint: 'country,new',
      dataSource: 'country_master'
    };

    // Use the same dynamic logic as the real component
    const allProperties = Object.keys(demoField);

    for (const prop of allProperties) {
      const value = demoField[prop as keyof typeof demoField];
      if (value && typeof value === 'string' && value.trim() !== '') {
        return value;
      }
    }

    return 'default';
  }
}
